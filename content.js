// Twitter User Info Viewer content script
// For each tweet, fetch the author's profile page and inject: joined date, following, followers, total posts

(function () {
  const cache = new Map(); // username -> { joinDate, following, followers, posts }
  const inFlight = new Map(); // username -> Promise

  const HOST = location.host.includes('twitter.com') ? 'https://twitter.com' : 'https://x.com';
  const PROFILE_HOSTS = [
    HOST, // Use same domain to avoid CORS issues
    'https://mobile.twitter.com' // fallback, but likely to fail due to CORS
  ];

  function getUsernameFromTweet(tweet) {
    // Method 1: Look for avatar image and trace back to username link
    // The avatar is usually the most reliable indicator of the tweet author
    const avatarImg = tweet.querySelector('img[src*="profile_images"]');
    if (avatarImg) {
      // Find the closest link that contains this avatar
      const avatarLink = avatarImg.closest('a[href^="/"]');
      if (avatarLink) {
        const href = avatarLink.getAttribute('href') || '';
        const parts = href.split('/').filter(Boolean);
        if (parts.length >= 1 && !href.includes('/status/') && !href.includes('/photo/') && !href.includes('/video/')) {
          console.log(`[TwitterUserInfo] Found username via avatar: ${parts[0]}`);
          return parts[0];
        }
      }
    }

    // Method 2: Look for the username link within the User-Name testid block
    // But be more specific about which link we choose
    const nameBlock = tweet.querySelector('[data-testid="User-Name"]');
    if (nameBlock) {
      // Look for links that are direct children or close descendants
      const links = nameBlock.querySelectorAll('a[href^="/"]');
      for (const link of links) {
        const href = link.getAttribute('href') || '';
        const parts = href.split('/').filter(Boolean);
        // Make sure it's a simple username link (not a status or media link)
        if (parts.length === 1 && /^[a-zA-Z0-9_]+$/.test(parts[0])) {
          console.log(`[TwitterUserInfo] Found username via User-Name block: ${parts[0]}`);
          return parts[0];
        }
      }
    }

    // Method 3: Look for the first link in the tweet that appears to be a username
    // This is more aggressive but should still avoid status links
    const allLinks = tweet.querySelectorAll('a[href^="/"]');
    for (const link of allLinks) {
      const href = link.getAttribute('href') || '';
      const parts = href.split('/').filter(Boolean);

      // Must be a simple username pattern and not contain status/media paths
      if (parts.length === 1 &&
          /^[a-zA-Z0-9_]+$/.test(parts[0]) &&
          !href.includes('/status/') &&
          !href.includes('/photo/') &&
          !href.includes('/video/') &&
          !href.includes('/hashtag/') &&
          !href.includes('/search/')) {

        // Additional check: make sure this link is in the upper part of the tweet (header area)
        const rect = link.getBoundingClientRect();
        const tweetRect = tweet.getBoundingClientRect();

        // If the link is in the top 40% of the tweet, it's likely the author
        if (rect.top - tweetRect.top < (tweetRect.height * 0.4)) {
          console.log(`[TwitterUserInfo] Found username via general search: ${parts[0]}`);
          return parts[0];
        }
      }
    }

    console.log('[TwitterUserInfo] Could not find username for tweet');
    return null;
  }

  function parseCountsText(text) {
    // Return text as-is; numbers can be 1,234 / 1.2K / 1.2万 etc.
    return (text || '').trim();
  }

  function parseProfileHTML(username, html) {
    const doc = new DOMParser().parseFromString(html, 'text/html');

    // Joined date: within UserProfileHeader_Items span containing Joined/加入
    let joinDate = '';
    const items = doc.querySelector('[data-testid="UserProfileHeader_Items"]')
      || doc.querySelector('div[aria-label*="Joined"], div[aria-label*="加入"]')
      || doc.querySelector('div:has(svg[viewBox="0 0 24 24"])');
    if (items) {
      const spans = Array.from(items.querySelectorAll('span'));
      for (const s of spans) {
        const t = s.textContent.trim();
        if (/Joined|加入/.test(t)) { joinDate = t; break; }
      }
    }
    // Mobile fallback: look for text "Joined" or "加入" in entire doc
    if (!joinDate) {
      const allSpans = Array.from(doc.querySelectorAll('span, div'));
      const hit = allSpans.find(n => /\bJoined\b|加入/.test(n.textContent || ''));
      if (hit) joinDate = hit.textContent.trim();
    }

    // Following and Followers: links end with /following and /followers
    let following = '';
    let followers = '';
    const followingLink = doc.querySelector(`a[href$="/${username}/following"], a[href*="/${username}/following?"]`)
      || doc.querySelector('a[href$="/following"]');
    const followersLink = doc.querySelector(`a[href$="/${username}/followers"], a[href*="/${username}/followers?"]`)
      || doc.querySelector('a[href$="/followers"]');
    // Prefer nested spans which often hold the number
    if (followingLink) {
      const num = followingLink.querySelector('span span');
      following = parseCountsText((num && num.textContent) || followingLink.textContent);
    }
    if (followersLink) {
      const num = followersLink.querySelector('span span');
      followers = parseCountsText((num && num.textContent) || followersLink.textContent);
    }

    // Total posts: a small div near the h2 name header like "1,823 帖子" or "1,823 Posts"
    let posts = '';
    const headerCounts = Array.from(doc.querySelectorAll('div, span'))
      .map(n => n.textContent && n.textContent.trim())
      .filter(Boolean);
    for (const t of headerCounts) {
      if (/\bPosts\b|帖子/.test(t)) { posts = t; break; }
    }
    // Mobile fallback: profile header count often like "Tweets", "推文" near tabs
    if (!posts) {
      const tabs = Array.from(doc.querySelectorAll('a, span')).map(n => (n.textContent || '').trim()).filter(Boolean);
      const hit = tabs.find(t => /\bTweets\b|推文|帖子/.test(t));
      if (hit) posts = hit;
    }

    // If DOM parsing failed, try extracting from embedded JSON
    function tryJson(key) {
      const re = new RegExp(`"${key}"\s*:\s*(".*?"|[-0-9]+)`, 'i');
      const m = html.match(re);
      if (!m) return null;
      const val = m[1];
      if (val == null) return null;
      if (val.startsWith('"') && val.endsWith('"')) return val.slice(1, -1);
      return Number(val);
    }

    function formatCount(n) {
      if (n == null || n === '' || n === '—') return '—';
      if (typeof n === 'string') n = Number(n.replace(/[,\s]/g, ''));
      if (!isFinite(n)) return '—';
      // Compact format (e.g., 1.2K, 3.4M)
      if (n >= 1e9) return (n / 1e9).toFixed(1).replace(/\.0$/, '') + 'B';
      if (n >= 1e6) return (n / 1e6).toFixed(1).replace(/\.0$/, '') + 'M';
      if (n >= 1e3) return (n / 1e3).toFixed(1).replace(/\.0$/, '') + 'K';
      return String(n);
    }

    function formatJoined(createdAt) {
      if (!createdAt || createdAt === '—') return '—';
      const d = new Date(createdAt);
      if (isNaN(d.getTime())) return createdAt; // fallback raw
      return `${d.getFullYear()}年${d.getMonth() + 1}月`;
    }

    if (!followers || followers === '—') {
      const v = tryJson('followers_count');
      if (v != null) followers = formatCount(v);
    }
    if (!following || following === '—') {
      const v = tryJson('friends_count');
      if (v != null) following = formatCount(v);
    }
    if (!posts || posts === '—') {
      const v = tryJson('statuses_count');
      if (v != null) posts = formatCount(v);
    }
    if (!joinDate || joinDate === '—') {
      const v = tryJson('created_at');
      if (v) joinDate = `Joined ${formatJoined(v)}`;
    }

    return {
      joinDate: joinDate || '—',
      following: following || '—',
      followers: followers || '—',
      posts: posts || '—'
    };
  }

  async function fetchProfile(username) {
    if (!username) return null;
    if (cache.has(username)) return cache.get(username);
    if (inFlight.has(username)) return inFlight.get(username);

    const p = (async () => {
      // First try to extract info from current page if we're on the user's profile
      if (location.pathname === `/${username}` || location.pathname.startsWith(`/${username}/`)) {
        console.log(`[TwitterUserInfo] Trying to extract info from current page for ${username}`);
        const currentPageInfo = parseProfileHTML(username, document.documentElement.outerHTML);
        if (currentPageInfo && (currentPageInfo.joinDate !== '—' || currentPageInfo.following !== '—' || currentPageInfo.followers !== '—' || currentPageInfo.posts !== '—')) {
          cache.set(username, currentPageInfo);
          return currentPageInfo;
        }
      }

      // Try fetching profile pages
      for (const base of PROFILE_HOSTS) {
        try {
          const url = `${base}/${encodeURIComponent(username)}`;
          console.log(`[TwitterUserInfo] Fetching profile from: ${url}`);
          const res = await fetch(url, {
            credentials: 'include',
            mode: 'cors',
            headers: {
              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
              'User-Agent': navigator.userAgent
            }
          });

          if (!res.ok) {
            console.log(`[TwitterUserInfo] HTTP ${res.status} for ${url}`);
            continue;
          }

          const txt = await res.text();
          const info = parseProfileHTML(username, txt);
          if (info && (info.joinDate !== '—' || info.following !== '—' || info.followers !== '—' || info.posts !== '—')) {
            console.log(`[TwitterUserInfo] Successfully fetched profile for ${username}:`, info);
            cache.set(username, info);
            return info;
          }
        } catch (e) {
          console.log(`[TwitterUserInfo] Error fetching ${base}/${username}:`, e.message);
          // try next host
        }
      }

      // As last resort, store blanks to avoid spamming requests
      console.log(`[TwitterUserInfo] Could not fetch profile for ${username}, using fallback`);
      const fallback = { joinDate: '—', following: '—', followers: '—', posts: '—' };
      cache.set(username, fallback);
      return fallback;
    })()
      .finally(() => {
        inFlight.delete(username);
      });
    inFlight.set(username, p);
    return p;
  }

  function ensureInfoContainer(tweet) {
    if (!tweet) return null;
    if (tweet.querySelector('.tweet-user-info-extension')) return null; // already added
    const host = tweet.querySelector('[data-testid="User-Name"]');
    if (!host) return null;
    const box = document.createElement('div');
    box.className = 'tweet-user-info-extension';
    Object.assign(box.style, {
      marginTop: '4px',
      fontSize: '12px',
      color: 'rgb(83, 100, 113)'
    });
    box.textContent = '加载用户信息中...';
    host.appendChild(box);
    return box;
  }

  function extractBasicInfoFromTweet(tweet, username) {
    // Try to extract some basic info from the tweet itself
    // This is a fallback when we can't fetch the full profile
    const info = { joinDate: '—', following: '—', followers: '—', posts: '—' };

    // Look for any follower count hints in the tweet (sometimes shown in verified badges, etc.)
    const textElements = tweet.querySelectorAll('span, div');
    for (const el of textElements) {
      const text = el.textContent || '';
      // Look for patterns like "1.2K followers", "关注者", etc.
      if (/\d+[KMB]?\s*(followers|关注者)/i.test(text)) {
        const match = text.match(/(\d+[KMB]?)\s*(followers|关注者)/i);
        if (match) info.followers = match[1];
      }
      if (/\d+[KMB]?\s*(following|正在关注)/i.test(text)) {
        const match = text.match(/(\d+[KMB]?)\s*(following|正在关注)/i);
        if (match) info.following = match[1];
      }
    }

    return info;
  }

  async function enhanceTweet(tweet) {
    if (!tweet || tweet.querySelector('.tweet-user-info-extension')) return;
    const username = getUsernameFromTweet(tweet);
    if (!username) return;

    const box = ensureInfoContainer(tweet);
    if (!box) return;

    // Add debug info to show which username we're fetching
    console.log(`[TwitterUserInfo] Processing tweet for username: ${username}`);

    let info = await fetchProfile(username);

    // If we couldn't get full profile info, try to extract basic info from tweet
    if (!info || (info.joinDate === '—' && info.following === '—' && info.followers === '—' && info.posts === '—')) {
      console.log(`[TwitterUserInfo] Trying to extract basic info from tweet for ${username}`);
      const basicInfo = extractBasicInfoFromTweet(tweet, username);
      info = { ...info, ...basicInfo };
    }

    if (!info) {
      box.textContent = '无法加载用户信息';
      return;
    }

    // Render compact line(s) with username for debugging
    const followingText = info.following.replace(/Following|正在关注/gi, '').trim();
    const followersText = info.followers.replace(/Followers|关注者/gi, '').trim();
    const postsText = info.posts.replace(/Posts?|帖子|推文/gi, '').trim();
    const joinText = info.joinDate.replace(/Joined\s*/i, '').trim();

    box.innerHTML = `
      <div style="font-size: 10px; color: #666; margin-bottom: 2px;">@${username}</div>
      ${joinText !== '—' ? `<div>加入: ${joinText}</div>` : ''}
      <div>关注: ${followingText} • 粉丝: ${followersText}${postsText !== '—' ? ` • 帖子: ${postsText}` : ''}</div>
    `;
  }

  function collectTweets(root = document) {
    return root.querySelectorAll('article[role="article"][data-testid="tweet"], article[role="article"]');
  }

  let scheduled = false;
  function scheduleScan() {
    if (scheduled) return;
    scheduled = true;
    requestAnimationFrame(() => {
      scheduled = false;
      const tweets = collectTweets();
      tweets.forEach(enhanceTweet);
    });
  }

  // Initial and delayed scans
  scheduleScan();
  window.addEventListener('load', scheduleScan, { once: true });
  setTimeout(scheduleScan, 1000);
  setTimeout(scheduleScan, 3000);
  setTimeout(scheduleScan, 6000);

  // Observe dynamic content changes
  const observer = new MutationObserver((muts) => {
    for (const m of muts) {
      if (m.addedNodes && m.addedNodes.length) {
        scheduleScan();
        break;
      }
    }
  });
  observer.observe(document.documentElement || document.body, { subtree: true, childList: true });
})();
